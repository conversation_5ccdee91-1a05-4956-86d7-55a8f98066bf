// Roles management JavaScript

let rolesData = [];
let currentDeleteRole = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
});

// Load roles from API
async function loadRoles() {
    let loadingTimeout;

    try {
        console.log('Starting to load roles...');
        utils.showLoading(true, 'Loading roles...');

        // Set a backup timeout to hide loading modal
        loadingTimeout = setTimeout(() => {
            console.warn('Loading timeout reached, hiding modal');
            utils.showLoading(false);
            utils.showAlert('Loading roles timed out. Please try again.', 'warning');
        }, 10000); // 10 second timeout

        const activeOnly = document.getElementById('activeOnly').checked;
        console.log('Making API call to /roles with activeOnly:', activeOnly);

        const response = await utils.apiGet(`/roles?active_only=${activeOnly}`);
        console.log('API response received:', response);

        // Clear the timeout since we got a response
        clearTimeout(loadingTimeout);

        rolesData = response.roles || [];
        renderRolesTable();

        console.log('Roles loaded successfully:', rolesData.length, 'roles');

    } catch (error) {
        console.error('Failed to load roles:', error);
        clearTimeout(loadingTimeout);
        utils.showAlert('Failed to load roles: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// Render roles table
function renderRolesTable() {
    try {
        console.log('Rendering roles table with data:', rolesData);
        const tbody = document.getElementById('rolesTableBody');

        if (!tbody) {
            console.error('rolesTableBody element not found');
            return;
        }

        if (!rolesData || rolesData.length === 0) {
            console.log('No roles data to display');
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <p>No roles found</p>
                    </td>
                </tr>
            `;
            return;
        }
    } catch (error) {
        console.error('Error in renderRolesTable:', error);
        utils.showAlert('Error rendering roles table: ' + error.message, 'danger');
        return;
    }

    try {
        tbody.innerHTML = rolesData.map(role => {
            console.log('Rendering role:', role.name);
            return `
                <tr>
                    <td>
                        <code>${utils.escapeHtml(role.name)}</code>
                    </td>
                    <td>${utils.escapeHtml(role.display_name)}</td>
                    <td>
                        <span class="text-truncate-2" title="${utils.escapeHtml(role.description || '')}">
                            ${utils.truncateText(role.description || 'No description', 50)}
                        </span>
                    </td>
                    <td>
                        <div class="d-flex flex-wrap gap-1">
                            ${(role.tools || []).slice(0, 3).map(tool =>
                                `<span class="badge bg-secondary">${utils.escapeHtml(tool)}</span>`
                            ).join('')}
                            ${(role.tools || []).length > 3 ?
                                `<span class="badge bg-light text-dark">+${(role.tools || []).length - 3} more</span>` :
                                ''
                            }
                        </div>
                    </td>
                    <td>
                        <span class="badge ${role.is_active ? 'bg-success' : 'bg-secondary'}">
                            ${role.is_active ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${role.created_at ? utils.formatRelativeTime(role.created_at) : 'Unknown'}
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="/roles/${role.name}/edit" class="btn btn-outline-primary" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-outline-info" onclick="testRole('${role.name}')" title="Test">
                                <i class="fas fa-flask"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteRole('${role.name}')" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        console.log('Roles table rendered successfully');
    } catch (error) {
        console.error('Error rendering roles HTML:', error);
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Error rendering roles: ${error.message}</p>
                </td>
            </tr>
        `;
    }
}

// Filter roles
function filterRoles() {
    const searchTerm = document.getElementById('searchRoles').value.toLowerCase();
    const activeOnly = document.getElementById('activeOnly').checked;
    
    let filteredRoles = rolesData;
    
    // Filter by active status
    if (activeOnly) {
        filteredRoles = filteredRoles.filter(role => role.is_active);
    }
    
    // Filter by search term
    if (searchTerm) {
        filteredRoles = filteredRoles.filter(role => 
            role.name.toLowerCase().includes(searchTerm) ||
            role.display_name.toLowerCase().includes(searchTerm) ||
            (role.description && role.description.toLowerCase().includes(searchTerm)) ||
            role.tools.some(tool => tool.toLowerCase().includes(searchTerm))
        );
    }
    
    // Update table with filtered data
    const originalData = rolesData;
    rolesData = filteredRoles;
    renderRolesTable();
    rolesData = originalData;
}

// Sync roles from YAML
async function syncRoles() {
    try {
        const confirmed = confirm('This will sync roles from the YAML configuration file. Continue?');
        if (!confirmed) return;
        
        utils.showLoading(true, 'Syncing roles...');
        
        const response = await utils.apiPost('/roles/sync', {});
        
        utils.showAlert(
            `Sync completed: ${response.created} created, ${response.updated} updated, ${response.skipped} skipped`,
            'success'
        );
        
        // Reload roles
        await loadRoles();
        
    } catch (error) {
        console.error('Failed to sync roles:', error);
        utils.showAlert('Failed to sync roles: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
    }
}

// Test role
function testRole(roleName) {
    // Redirect to test page with role parameter
    window.location.href = `/test?role=${encodeURIComponent(roleName)}`;
}

// Delete role
function deleteRole(roleName) {
    currentDeleteRole = roleName;
    document.getElementById('deleteRoleName').textContent = roleName;
    document.getElementById('hardDelete').checked = false;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Confirm delete
async function confirmDelete() {
    if (!currentDeleteRole) return;
    
    try {
        const hardDelete = document.getElementById('hardDelete').checked;
        
        utils.showLoading(true, 'Deleting role...');
        
        await utils.apiDelete(`/roles/${currentDeleteRole}?hard_delete=${hardDelete}`);
        
        utils.showAlert(`Role "${currentDeleteRole}" deleted successfully`, 'success');
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
        
        // Reload roles
        await loadRoles();
        
    } catch (error) {
        console.error('Failed to delete role:', error);
        utils.showAlert('Failed to delete role: ' + error.message, 'danger');
    } finally {
        utils.showLoading(false);
        currentDeleteRole = null;
    }
}

// Event listeners
document.getElementById('activeOnly').addEventListener('change', filterRoles);
document.getElementById('searchRoles').addEventListener('input', filterRoles);

// Auto-refresh every 30 seconds
setInterval(loadRoles, 30000);
